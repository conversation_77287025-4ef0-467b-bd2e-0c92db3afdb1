"""
四层思维链分析器
实现"由外向内、由宏观到微观、由影响到标的"的四层漏斗模型分析
"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class LayerAnalysisResult:
    """单层分析结果"""
    layer_name: str
    analysis_content: str
    key_findings: List[str]
    confidence_score: float
    next_layer_inputs: Dict[str, Any]

@dataclass
class FourLayerAnalysisResult:
    """四层分析完整结果"""
    layer1_result: LayerAnalysisResult  # 事件感知
    layer2_result: LayerAnalysisResult  # 深度挖掘
    layer3_result: LayerAnalysisResult  # 国内影响
    layer4_result: LayerAnalysisResult  # 标的筛选
    final_investment_targets: List[Dict[str, Any]]
    overall_confidence: float
    analysis_summary: str

class FourLayerThinkingAnalyzer:
    """四层思维链分析器"""
    
    def __init__(self, llm_manager):
        self.llm_manager = llm_manager
        
    def analyze_news_event(
        self, 
        news_data: Dict[str, Any],
        research_summaries: List[str]
    ) -> FourLayerAnalysisResult:
        """
        对新闻事件进行四层思维链分析
        
        Args:
            news_data: 新闻数据
            research_summaries: 研究总结列表
            
        Returns:
            四层分析结果
        """
        try:
            # 第一层：事件感知与直接联想
            layer1_result = self._analyze_layer1_event_perception(
                news_data, research_summaries
            )
            
            # 第二层：深挖供应链与关键信息
            layer2_result = self._analyze_layer2_supply_chain(
                news_data, research_summaries, layer1_result
            )
            
            # 第三层：聚焦国内产业与市场动态
            layer3_result = self._analyze_layer3_domestic_impact(
                news_data, research_summaries, layer2_result
            )
            
            # 第四层：筛选与锁定具体上市公司
            layer4_result = self._analyze_layer4_target_selection(
                news_data, research_summaries, layer3_result
            )
            
            # 生成最终投资标的
            final_targets = self._generate_final_investment_targets(
                layer4_result
            )
            
            # 计算整体置信度
            overall_confidence = self._calculate_overall_confidence([
                layer1_result, layer2_result, layer3_result, layer4_result
            ])
            
            # 生成分析总结
            analysis_summary = self._generate_analysis_summary(
                layer1_result, layer2_result, layer3_result, layer4_result
            )
            
            return FourLayerAnalysisResult(
                layer1_result=layer1_result,
                layer2_result=layer2_result,
                layer3_result=layer3_result,
                layer4_result=layer4_result,
                final_investment_targets=final_targets,
                overall_confidence=overall_confidence,
                analysis_summary=analysis_summary
            )
            
        except Exception as e:
            logger.error(f"四层思维链分析失败: {e}")
            raise
    
    def _analyze_layer1_event_perception(
        self, 
        news_data: Dict[str, Any], 
        research_summaries: List[str]
    ) -> LayerAnalysisResult:
        """第一层：事件感知与直接联想分析"""
        
        prompt = self._build_layer1_prompt(news_data, research_summaries)
        
        response = self.llm_manager.get_gemini_response(
            prompt,
            model="gemini-2.0-flash-exp",
            temperature=0.3
        )
        
        # 解析响应并提取关键信息
        key_findings = self._extract_key_findings(response, "layer1")
        next_layer_inputs = self._extract_next_layer_inputs(response, "layer1")
        
        return LayerAnalysisResult(
            layer_name="第一层：事件感知与直接联想",
            analysis_content=response,
            key_findings=key_findings,
            confidence_score=0.8,
            next_layer_inputs=next_layer_inputs
        )
    
    def _analyze_layer2_supply_chain(
        self, 
        news_data: Dict[str, Any], 
        research_summaries: List[str],
        layer1_result: LayerAnalysisResult
    ) -> LayerAnalysisResult:
        """第二层：深挖供应链与关键信息分析"""
        
        prompt = self._build_layer2_prompt(
            news_data, research_summaries, layer1_result
        )
        
        response = self.llm_manager.get_gemini_response(
            prompt,
            model="gemini-2.0-flash-exp",
            temperature=0.5
        )
        
        key_findings = self._extract_key_findings(response, "layer2")
        next_layer_inputs = self._extract_next_layer_inputs(response, "layer2")
        
        return LayerAnalysisResult(
            layer_name="第二层：深挖供应链与关键信息",
            analysis_content=response,
            key_findings=key_findings,
            confidence_score=0.9,  # 这一层是关键的信息差层
            next_layer_inputs=next_layer_inputs
        )
    
    def _analyze_layer3_domestic_impact(
        self, 
        news_data: Dict[str, Any], 
        research_summaries: List[str],
        layer2_result: LayerAnalysisResult
    ) -> LayerAnalysisResult:
        """第三层：聚焦国内产业与市场动态分析"""
        
        prompt = self._build_layer3_prompt(
            news_data, research_summaries, layer2_result
        )
        
        response = self.llm_manager.get_gemini_response(
            prompt,
            model="gemini-2.0-flash-exp",
            temperature=0.4
        )
        
        key_findings = self._extract_key_findings(response, "layer3")
        next_layer_inputs = self._extract_next_layer_inputs(response, "layer3")
        
        return LayerAnalysisResult(
            layer_name="第三层：聚焦国内产业与市场动态",
            analysis_content=response,
            key_findings=key_findings,
            confidence_score=0.85,
            next_layer_inputs=next_layer_inputs
        )
    
    def _analyze_layer4_target_selection(
        self, 
        news_data: Dict[str, Any], 
        research_summaries: List[str],
        layer3_result: LayerAnalysisResult
    ) -> LayerAnalysisResult:
        """第四层：筛选与锁定具体上市公司分析"""
        
        prompt = self._build_layer4_prompt(
            news_data, research_summaries, layer3_result
        )
        
        response = self.llm_manager.get_gemini_response(
            prompt,
            model="gemini-2.0-flash-exp",
            temperature=0.2  # 最终选择需要更精确
        )
        
        key_findings = self._extract_key_findings(response, "layer4")
        next_layer_inputs = self._extract_next_layer_inputs(response, "layer4")
        
        return LayerAnalysisResult(
            layer_name="第四层：筛选与锁定具体上市公司",
            analysis_content=response,
            key_findings=key_findings,
            confidence_score=0.75,
            next_layer_inputs=next_layer_inputs
        )
    
    def _build_layer1_prompt(
        self,
        news_data: Dict[str, Any],
        research_summaries: List[str]
    ) -> str:
        """构建第一层分析提示词"""

        news_title = news_data.get('title', '')
        news_content = news_data.get('content', '')
        news_source = news_data.get('source', '未知来源')
        publish_time = news_data.get('publish_time', '未知时间')
        summaries_text = "\n".join([f"- {summary}" for summary in research_summaries])

        return f"""
# 第一层分析：事件感知与直接联想

你是一位顶级投资分析师，请严格按照以下框架对新闻事件进行第一层深度分析。

## 新闻信息
- 标题：{news_title}
- 内容：{news_content}
- 来源：{news_source}
- 发布时间：{publish_time}

## 研究资料
{summaries_text}

## 分析框架要求

### 1.1 可信度评估（1-10分制，必须提供评分依据）
请从以下维度进行评估：
- 信息来源权威性（权重30%）
- 事实核实程度（权重25%）
- 逻辑一致性（权重20%）
- 数据支撑度（权重15%）
- 时效性（权重10%）

**输出格式：**
```
可信度评分：X/10分
评分依据：
- 信息来源权威性：X分（具体说明）
- 事实核实程度：X分（具体说明）
- 逻辑一致性：X分（具体说明）
- 数据支撑度：X分（具体说明）
- 时效性：X分（具体说明）
综合评估：[详细说明]
```

### 1.2 核心要素提取（必须包含所有要素）
**输出格式：**
```
核心要素分析：
- 时间：[具体时间节点及其重要性]
- 地点：[具体地理位置及其战略意义]
- 关键主体：[主要参与方及其角色]
- 影响范围：[直接影响范围+间接影响范围]
- 紧急程度：[1-10分，并说明评分理由]
```

### 1.3 第一性原理推导（至少4条具体影响路径）
运用第一性原理，推导出至少4条具体的直接影响路径：

**输出格式：**
```
直接影响路径分析：
路径1：[事件] → [中间环节] → [最终影响] （影响程度：X%，时间框架：X）
路径2：[事件] → [中间环节] → [最终影响] （影响程度：X%，时间框架：X）
路径3：[事件] → [中间环节] → [最终影响] （影响程度：X%，时间框架：X）
路径4：[事件] → [中间环节] → [最终影响] （影响程度：X%，时间框架：X）
```

### 1.4 大众认知预期和盲点识别
**输出格式：**
```
大众认知分析：
预期热点领域（红海区域）：
1. [具体领域]：[大众关注原因] - [为什么是红海]
2. [具体领域]：[大众关注原因] - [为什么是红海]
3. [具体领域]：[大众关注原因] - [为什么是红海]

大众认知盲点：
1. [被忽视的领域]：[为什么被忽视] - [实际影响程度]
2. [被忽视的领域]：[为什么被忽视] - [实际影响程度]
3. [被忽视的领域]：[为什么被忽视] - [实际影响程度]
```

### 1.5 深挖方向（为第二层分析提供5个具体线索）
**输出格式：**
```
深挖方向指引：
方向1：[具体调研方向] - [预期发现的信息差] - [重要性评级：1-10分]
方向2：[具体调研方向] - [预期发现的信息差] - [重要性评级：1-10分]
方向3：[具体调研方向] - [预期发现的信息差] - [重要性评级：1-10分]
方向4：[具体调研方向] - [预期发现的信息差] - [重要性评级：1-10分]
方向5：[具体调研方向] - [预期发现的信息差] - [重要性评级：1-10分]
```

## 质量要求
- 避免空洞的列表项，每个要点必须有实质性内容
- 所有分析必须基于具体数据和事实
- 量化分析优于定性描述
- 重点关注信息差挖掘机会

**请严格按照上述格式输出，确保每个部分都有具体、详实的内容。**
"""
    
    def _build_layer2_prompt(
        self,
        news_data: Dict[str, Any],
        research_summaries: List[str],
        layer1_result: LayerAnalysisResult
    ) -> str:
        """构建第二层分析提示词"""

        news_title = news_data.get('title', '')
        summaries_text = "\n".join([f"- {summary}" for summary in research_summaries])
        layer1_content = layer1_result.analysis_content

        return f"""
# 第二层分析：深挖供应链与关键信息（信息差挖掘层）

这是四层分析中最关键的信息差挖掘层，请深度挖掘被大众忽视的关键信息。

## 新闻标题
{news_title}

## 第一层分析结果
{layer1_content}

## 研究资料
{summaries_text}

## 分析框架要求

### 2.1 核心受影响主体识别（包括被大众忽视的主体）
**输出格式：**
```
核心受影响主体分析：
直接受影响主体：
1. [主体名称]：[具体影响] - [影响程度：X%] - [时间框架：X]
2. [主体名称]：[具体影响] - [影响程度：X%] - [时间框架：X]
3. [主体名称]：[具体影响] - [影响程度：X%] - [时间框架：X]

被大众忽视但实际受重大影响的主体：
1. [主体名称]：[为什么被忽视] - [实际影响程度：X%] - [信息差价值：高/中/低]
2. [主体名称]：[为什么被忽视] - [实际影响程度：X%] - [信息差价值：高/中/低]
3. [主体名称]：[为什么被忽视] - [实际影响程度：X%] - [信息差价值：高/中/低]
```

### 2.2 全球供应链角色解构（重点关注"不为人知的关键商品"）
**输出格式：**
```
供应链角色分析：
主要产品/服务：
- [产品名称]：[全球市场份额：X%] - [主要出口国] - [年产量：X万吨/亿美元]
- [产品名称]：[全球市场份额：X%] - [主要出口国] - [年产量：X万吨/亿美元]

不为人知的关键商品：
- [商品名称]：[用途] - [全球依赖度：X%] - [替代难度：高/中/低] - [价格敏感性：X%]
- [商品名称]：[用途] - [全球依赖度：X%] - [替代难度：高/中/低] - [价格敏感性：X%]

供应链关键节点：
- [节点名称]：[在供应链中的位置] - [断供风险：高/中/低] - [影响范围]
```

### 2.3 贸易伙伴关系追溯（量化贸易依存度）
**输出格式：**
```
贸易关系量化分析：
主要贸易伙伴：
1. [国家/地区]：[贸易额：X亿美元] - [依存度：X%] - [主要商品：X、Y、Z]
2. [国家/地区]：[贸易额：X亿美元] - [依存度：X%] - [主要商品：X、Y、Z]
3. [国家/地区]：[贸易额：X亿美元] - [依存度：X%] - [主要商品：X、Y、Z]

供应中断连锁反应：
- 第一波影响：[X天内] - [影响行业：A、B、C] - [影响程度：X%]
- 第二波影响：[X周内] - [影响行业：D、E、F] - [影响程度：X%]
- 第三波影响：[X月内] - [影响行业：G、H、I] - [影响程度：X%]
```

### 2.4 影响程度量化分析（具体数据、替代方案、供需缺口时间）
**输出格式：**
```
影响程度量化：
供应中断影响：
- [商品/服务]：[供应缺口：X万吨/X%] - [价格影响：+X%] - [持续时间：X个月]
- [商品/服务]：[供应缺口：X万吨/X%] - [价格影响：+X%] - [持续时间：X个月]

替代方案分析：
- [替代来源1]：[可替代量：X%] - [成本增加：+X%] - [实现时间：X个月]
- [替代来源2]：[可替代量：X%] - [成本增加：+X%] - [实现时间：X个月]

供需缺口时间表：
- 短期（1-3个月）：[缺口：X%] - [主要影响：X]
- 中期（3-12个月）：[缺口：X%] - [主要影响：X]
- 长期（1年以上）：[缺口：X%] - [主要影响：X]
```

### 2.5 关键信息差发现（为第三层国内分析提供6个具体线索）
**输出格式：**
```
信息差发现总结：
线索1：[具体发现] - [对国内市场的潜在影响] - [信息差价值：1-10分]
线索2：[具体发现] - [对国内市场的潜在影响] - [信息差价值：1-10分]
线索3：[具体发现] - [对国内市场的潜在影响] - [信息差价值：1-10分]
线索4：[具体发现] - [对国内市场的潜在影响] - [信息差价值：1-10分]
线索5：[具体发现] - [对国内市场的潜在影响] - [信息差价值：1-10分]
线索6：[具体发现] - [对国内市场的潜在影响] - [信息差价值：1-10分]
```

## 质量要求
- 所有数据必须具体化，避免模糊表述
- 重点挖掘大众认知盲点中的投资机会
- 量化分析优于定性描述
- 每个发现都要说明其信息差价值

**请严格按照上述格式输出，确保每个数据点都有具体数值支撑。**
"""

    def _build_layer3_prompt(
        self,
        news_data: Dict[str, Any],
        research_summaries: List[str],
        layer2_result: LayerAnalysisResult
    ) -> str:
        """构建第三层分析提示词"""

        news_title = news_data.get('title', '')
        summaries_text = "\n".join([f"- {summary}" for summary in research_summaries])
        layer2_content = layer2_result.analysis_content

        return f"""
# 第三层分析：聚焦国内产业与市场动态

基于前两层分析，现在将全球供应链影响具体化到国内市场。

## 新闻标题
{news_title}

## 第二层分析结果
{layer2_content}

## 研究资料
{summaries_text}

## 分析框架要求

### 3.1 国内受影响行业识别（直接影响+间接影响）
**输出格式：**
```
国内受影响行业分析：
直接影响行业：
1. [行业名称]：[影响机制] - [影响程度：+/-X%] - [影响时间：X个月] - [行业规模：X万亿元]
2. [行业名称]：[影响机制] - [影响程度：+/-X%] - [影响时间：X个月] - [行业规模：X万亿元]
3. [行业名称]：[影响机制] - [影响程度：+/-X%] - [影响时间：X个月] - [行业规模：X万亿元]

间接影响行业：
1. [行业名称]：[传导路径] - [影响程度：+/-X%] - [滞后时间：X个月] - [行业规模：X万亿元]
2. [行业名称]：[传导路径] - [影响程度：+/-X%] - [滞后时间：X个月] - [行业规模：X万亿元]
3. [行业名称]：[传导路径] - [影响程度：+/-X%] - [滞后时间：X个月] - [行业规模：X万亿元]

细分领域机会：
- [细分领域]：[具体机会] - [市场空间：X亿元] - [增长预期：+X%]
- [细分领域]：[具体机会] - [市场空间：X亿元] - [增长预期：+X%]
```

### 3.2 供需变化量化分析（供给端、需求端、供需平衡）
**输出格式：**
```
供需变化量化：
供给端分析：
- [商品/服务]：[供给变化：+/-X%] - [产能影响：X万吨] - [时间框架：X个月]
- [商品/服务]：[供给变化：+/-X%] - [产能影响：X万吨] - [时间框架：X个月]

需求端分析：
- [商品/服务]：[需求变化：+/-X%] - [需求弹性：X] - [替代效应：X%]
- [商品/服务]：[需求变化：+/-X%] - [需求弹性：X] - [替代效应：X%]

供需平衡：
- [商品/服务]：[供需缺口：+/-X%] - [平衡恢复时间：X个月] - [结构性变化：是/否]
- [商品/服务]：[供需缺口：+/-X%] - [平衡恢复时间：X个月] - [结构性变化：是/否]
```

### 3.3 价格传导机制和预期涨幅（具体百分比预测）
**输出格式：**
```
价格传导分析：
上游价格变化：
- [原材料]：[价格变化：+/-X%] - [传导系数：X] - [传导时间：X周]
- [原材料]：[价格变化：+/-X%] - [传导系数：X] - [传导时间：X周]

中游价格传导：
- [中间产品]：[价格变化：+/-X%] - [成本占比：X%] - [转嫁能力：X%]
- [中间产品]：[价格变化：+/-X%] - [成本占比：X%] - [转嫁能力：X%]

下游终端价格：
- [终端产品]：[价格变化：+/-X%] - [消费者承受度：高/中/低] - [需求影响：-X%]
- [终端产品]：[价格变化：+/-X%] - [消费者承受度：高/中/低] - [需求影响：-X%]

预期涨幅预测：
- 短期（1-3个月）：[产品类别] 预期涨幅 +X%
- 中期（3-12个月）：[产品类别] 预期涨幅 +X%
- 长期（1年以上）：[产品类别] 预期涨幅 +X%
```

### 3.4 受益方/受损方筛选标准（为第四层公司筛选提供明确标准）
**输出格式：**
```
公司筛选标准：
受益方筛选标准：
1. 业务相关性：[相关业务收入占比 ≥ X%]
2. 产能规模：[行业排名前X位 或 市场份额 ≥ X%]
3. 成本结构：[受益成本项占总成本 ≤ X%]
4. 定价能力：[历史提价成功率 ≥ X%]
5. 财务健康：[资产负债率 ≤ X% 且 现金流 > 0]

受损方识别标准：
1. 成本敏感性：[受影响成本占总成本 ≥ X%]
2. 转嫁能力：[历史提价成功率 ≤ X%]
3. 替代难度：[原材料替代难度：高/中/低]
4. 库存周期：[库存周转天数 ≥ X天]
5. 财务脆弱性：[资产负债率 ≥ X% 或 现金流 < 0]

优先筛选方向：
- 高纯度标的：[业务占比 ≥ X%] + [行业地位前X]
- 弹性标的：[业绩弹性系数 ≥ X] + [历史波动率适中]
- 确定性标的：[业务稳定性高] + [财务健康] + [竞争优势明显]
```

## 质量要求
- 所有百分比和数值必须基于合理估算
- 重点关注可量化的投资机会
- 避免宽泛的行业描述，聚焦具体细分领域
- 为第四层标的筛选提供可操作的具体标准

**请严格按照上述格式输出，确保每个预测都有具体的数值和时间框架。**
"""

    def _build_layer4_prompt(
        self,
        news_data: Dict[str, Any],
        research_summaries: List[str],
        layer3_result: LayerAnalysisResult
    ) -> str:
        """构建第四层分析提示词"""

        news_title = news_data.get('title', '')
        summaries_text = "\n".join([f"- {summary}" for summary in research_summaries])
        layer3_content = layer3_result.analysis_content

        return f"""
# 第四层分析：筛选与锁定具体上市公司

基于前三层分析，现在进行最终的投资标的精准筛选。

## 新闻标题
{news_title}

## 第三层分析结果
{layer3_content}

## 研究资料
{summaries_text}

## 分析框架要求

### 4.1 候选标的池建立（A股、港股、美股各至少3个标的）
**输出格式：**
```
候选标的池：
A股标的：
1. [公司名称]([股票代码])：[主营业务] - [业务相关性：X%] - [市值：X亿元]
2. [公司名称]([股票代码])：[主营业务] - [业务相关性：X%] - [市值：X亿元]
3. [公司名称]([股票代码])：[主营业务] - [业务相关性：X%] - [市值：X亿元]

港股标的：
1. [公司名称]([股票代码])：[主营业务] - [业务相关性：X%] - [市值：X亿港元]
2. [公司名称]([股票代码])：[主营业务] - [业务相关性：X%] - [市值：X亿港元]
3. [公司名称]([股票代码])：[主营业务] - [业务相关性：X%] - [市值：X亿港元]

美股标的：
1. [公司名称]([股票代码])：[主营业务] - [业务相关性：X%] - [市值：X亿美元]
2. [公司名称]([股票代码])：[主营业务] - [业务相关性：X%] - [市值：X亿美元]
3. [公司名称]([股票代码])：[主营业务] - [业务相关性：X%] - [市值：X亿美元]
```

### 4.2 精准画像分析（业务占比、产能规模、业绩弹性、财务健康度）
**输出格式：**
```
标的精准画像：
[公司名称1]：
- 业务占比：[相关业务收入占比X%] - [具体业务描述]
- 产能规模：[行业排名第X位] - [市场份额X%] - [年产能X万吨]
- 业绩弹性：[价格每上涨1%，业绩提升X%] - [弹性系数：X]
- 财务健康度：[资产负债率X%] - [ROE：X%] - [现金流：X亿元]
- 竞争优势：[核心优势1、2、3]

[公司名称2]：
- 业务占比：[相关业务收入占比X%] - [具体业务描述]
- 产能规模：[行业排名第X位] - [市场份额X%] - [年产能X万吨]
- 业绩弹性：[价格每上涨1%，业绩提升X%] - [弹性系数：X]
- 财务健康度：[资产负债率X%] - [ROE：X%] - [现金流：X亿元]
- 竞争优势：[核心优势1、2、3]

[继续其他标的...]
```

### 4.3 综合评分体系并排序（权重分配要明确）
**输出格式：**
```
综合评分体系：
评分维度及权重：
- 业务相关性（权重30%）：[评分标准]
- 业绩弹性（权重25%）：[评分标准]
- 财务健康度（权重20%）：[评分标准]
- 行业地位（权重15%）：[评分标准]
- 流动性（权重10%）：[评分标准]

标的评分排序：
1. [公司名称]：[总分X分] = [业务相关性X分×30%] + [业绩弹性X分×25%] + [财务健康度X分×20%] + [行业地位X分×15%] + [流动性X分×10%]
2. [公司名称]：[总分X分] = [详细计算过程]
3. [公司名称]：[总分X分] = [详细计算过程]
[继续排序...]
```

### 4.4 风险收益评估（潜在收益空间、主要风险因素、风险调整后收益预期）
**输出格式：**
```
风险收益评估：
[公司名称1]：
- 潜在收益空间：[乐观情形+X%] - [中性情形+X%] - [悲观情形+X%]
- 主要风险因素：[风险1：影响程度X%] - [风险2：影响程度X%] - [风险3：影响程度X%]
- 风险调整后收益：[期望收益X%] - [风险系数X] - [夏普比率X]
- 时间框架：[短期X个月] - [中期X个月] - [长期X年]

[公司名称2]：
[同样格式...]
```

## 最终输出要求

### 核心推荐标的（1-3个）
**输出格式：**
```
核心推荐标的：
1. [公司名称]([股票代码]) - [当前价格：X元]
   推荐理由：[具体理由，包含业务相关性、竞争优势、财务状况]
   预期收益空间：[目标价X元，上涨空间X%]
   时间框架：[X个月内]
   置信度：[X%]

2. [公司名称]([股票代码]) - [当前价格：X元]
   [同样格式...]
```

### 备选标的（2-3个）
**输出格式：**
```
备选标的：
1. [公司名称]([股票代码])
   基本信息：[市值、主营业务、行业地位]
   推荐逻辑：[为什么是备选，与核心标的的差异]
   预期收益：[X%]

2. [公司名称]([股票代码])
   [同样格式...]
```

### 整体分析置信度评估（0-1分制，需说明评分依据）
**输出格式：**
```
整体置信度评估：
置信度评分：X.X分（满分1.0分）
评分依据：
- 信息完整度：[X.X分] - [说明]
- 逻辑严密性：[X.X分] - [说明]
- 数据支撑度：[X.X分] - [说明]
- 市场验证度：[X.X分] - [说明]
- 时效性：[X.X分] - [说明]
```

## 质量要求
- 每个推荐标的必须有具体的股票代码和当前价格
- 所有收益预期必须有明确的时间框架
- 风险评估要具体量化，避免模糊表述
- 置信度评估要有详细的评分依据

**请严格按照上述格式输出，确保每个推荐都有充分的数据支撑和明确的投资逻辑。**
"""

    def _extract_key_findings(self, response: str, layer: str) -> List[str]:
        """从分析响应中提取关键发现"""
        try:
            findings = []

            # 根据不同层级提取不同类型的关键信息
            if layer == "layer1":
                # 第一层：提取可信度评分、核心要素、影响路径等
                patterns = [
                    r'可信度评分：(\d+/10分)',
                    r'影响程度：([+-]?\d+%)',
                    r'紧急程度：(\d+分)',
                    r'时间框架：([^，。\n]+)',
                    r'关键主体：([^，。\n]+)'
                ]
            elif layer == "layer2":
                # 第二层：提取供应链数据、贸易依存度等
                patterns = [
                    r'全球市场份额：(\d+%)',
                    r'贸易额：([^，。\n]+)',
                    r'依存度：(\d+%)',
                    r'供应缺口：([+-]?\d+%)',
                    r'价格影响：([+-]?\d+%)'
                ]
            elif layer == "layer3":
                # 第三层：提取行业影响、价格预期等
                patterns = [
                    r'影响程度：([+-]?\d+%)',
                    r'预期涨幅：([+-]?\d+%)',
                    r'市场空间：([^，。\n]+)',
                    r'行业规模：([^，。\n]+)',
                    r'增长预期：([+-]?\d+%)'
                ]
            elif layer == "layer4":
                # 第四层：提取投资标的、评分等
                patterns = [
                    r'([^（]+)\(([^）]+)\)',  # 提取公司名称和股票代码
                    r'总分(\d+\.?\d*)分',
                    r'预期收益：([+-]?\d+%)',
                    r'置信度：(\d+%)',
                    r'目标价([^，。\n]+)'
                ]
            else:
                patterns = []

            # 使用正则表达式提取关键信息
            import re
            for pattern in patterns:
                matches = re.findall(pattern, response)
                for match in matches:
                    if isinstance(match, tuple):
                        findings.append(f"{match[0]}({match[1]})")
                    else:
                        findings.append(str(match))

            # 如果正则提取失败，回退到关键词提取
            if not findings:
                lines = response.split('\n')
                for line in lines:
                    line = line.strip()
                    if any(keyword in line for keyword in ['关键', '重要', '核心', '主要', '显著', '推荐', '评分']):
                        if 10 < len(line) < 200:
                            findings.append(line)

            # 限制数量并去重
            findings = list(dict.fromkeys(findings))  # 去重
            return findings[:8]  # 增加到8个关键发现

        except Exception as e:
            logger.warning(f"提取关键发现失败: {e}")
            return [f"{layer}分析完成"]

    def _extract_next_layer_inputs(self, response: str, layer: str) -> Dict[str, Any]:
        """提取传递给下一层的输入信息"""
        try:
            # 提取关键数据点传递给下一层
            inputs = {
                "analysis_content": response,
                "layer": layer,
                "timestamp": "2025-06-19"  # 可以使用实际时间戳
            }

            # 根据不同层级提取不同的关键信息
            if layer == "layer1":
                inputs["event_type"] = "市场事件"
                inputs["urgency_level"] = "高"
            elif layer == "layer2":
                inputs["supply_chain_impact"] = "供应链中断"
                inputs["trade_dependency"] = "高依存度"
            elif layer == "layer3":
                inputs["domestic_sectors"] = ["化工", "能源"]
                inputs["price_impact"] = "上涨预期"

            return inputs

        except Exception as e:
            logger.warning(f"提取下层输入失败: {e}")
            return {"layer": layer}

    def _generate_final_investment_targets(
        self,
        layer4_result: LayerAnalysisResult
    ) -> List[Dict[str, Any]]:
        """生成最终投资标的列表"""
        try:
            import re
            targets = []
            response = layer4_result.analysis_content

            # 提取核心推荐标的
            core_targets_section = re.search(r'核心推荐标的：(.*?)(?=备选标的：|$)', response, re.DOTALL)
            if core_targets_section:
                core_content = core_targets_section.group(1)

                # 提取每个推荐标的的信息
                target_pattern = r'(\d+)\.\s*([^（]+)\(([^）]+)\)[^推]*推荐理由：([^预]*?)预期收益空间：([^时]*?)时间框架：([^置]*?)置信度：([^%\n]*%)'
                matches = re.findall(target_pattern, core_content, re.DOTALL)

                for match in matches:
                    target = {
                        "rank": int(match[0]),
                        "name": match[1].strip(),
                        "symbol": match[2].strip(),
                        "recommendation_reason": match[3].strip(),
                        "expected_return": match[4].strip(),
                        "time_frame": match[5].strip(),
                        "confidence": self._parse_confidence(match[6].strip()),
                        "target_type": "核心推荐"
                    }
                    targets.append(target)

            # 提取备选标的
            backup_targets_section = re.search(r'备选标的：(.*?)(?=整体分析置信度|$)', response, re.DOTALL)
            if backup_targets_section:
                backup_content = backup_targets_section.group(1)

                # 简化的备选标的提取
                backup_pattern = r'(\d+)\.\s*([^（]+)\(([^）]+)\)[^基]*基本信息：([^推]*?)推荐逻辑：([^预]*?)预期收益：([^%\n]*%)'
                matches = re.findall(backup_pattern, backup_content, re.DOTALL)

                for match in matches:
                    target = {
                        "rank": int(match[0]) + 10,  # 备选标的排序在核心推荐之后
                        "name": match[1].strip(),
                        "symbol": match[2].strip(),
                        "basic_info": match[3].strip(),
                        "recommendation_reason": match[4].strip(),
                        "expected_return": match[5].strip(),
                        "time_frame": "中期",
                        "confidence": 0.6,  # 备选标的默认置信度
                        "target_type": "备选推荐"
                    }
                    targets.append(target)

            # 如果没有提取到标的，创建默认示例
            if not targets:
                logger.warning("未能从分析结果中提取到投资标的，使用默认示例")
                targets = [
                    {
                        "rank": 1,
                        "name": "待分析公司1",
                        "symbol": "000000",
                        "recommendation_reason": "基于四层分析框架识别的投资机会",
                        "expected_return": "待评估",
                        "time_frame": "3-6个月",
                        "confidence": 0.7,
                        "target_type": "核心推荐"
                    }
                ]

            # 按排序和置信度排序
            targets.sort(key=lambda x: (x["rank"], -x["confidence"]))

            return targets[:5]  # 最多返回5个标的

        except Exception as e:
            logger.error(f"生成投资标的失败: {e}")
            return [
                {
                    "rank": 1,
                    "name": "分析处理中",
                    "symbol": "000000",
                    "recommendation_reason": "四层分析框架处理中，请稍后查看详细结果",
                    "expected_return": "待评估",
                    "time_frame": "待确定",
                    "confidence": 0.5,
                    "target_type": "处理中"
                }
            ]

    def _parse_confidence(self, confidence_str: str) -> float:
        """解析置信度字符串为浮点数"""
        try:
            # 提取百分比数字
            import re
            match = re.search(r'(\d+(?:\.\d+)?)%?', confidence_str)
            if match:
                value = float(match.group(1))
                # 如果是百分比形式，转换为小数
                if value > 1:
                    return value / 100
                return value
            return 0.7  # 默认置信度
        except:
            return 0.7

    def _calculate_overall_confidence(
        self,
        layer_results: List[LayerAnalysisResult]
    ) -> float:
        """计算整体分析置信度"""
        try:
            if not layer_results:
                return 0.5

            # 加权平均，第二层（信息差层）权重最高
            weights = [0.2, 0.4, 0.25, 0.15]  # 对应四层的权重

            total_score = 0
            total_weight = 0

            for i, result in enumerate(layer_results):
                if i < len(weights):
                    total_score += result.confidence_score * weights[i]
                    total_weight += weights[i]

            return total_score / total_weight if total_weight > 0 else 0.5

        except Exception as e:
            logger.warning(f"计算置信度失败: {e}")
            return 0.5

    def _generate_analysis_summary(
        self,
        layer1_result: LayerAnalysisResult,
        layer2_result: LayerAnalysisResult,
        layer3_result: LayerAnalysisResult,
        layer4_result: LayerAnalysisResult
    ) -> str:
        """生成分析总结"""
        try:
            # 提取各层关键发现
            layer1_key = layer1_result.key_findings[:2] if layer1_result.key_findings else ["事件感知分析完成"]
            layer2_key = layer2_result.key_findings[:2] if layer2_result.key_findings else ["供应链分析完成"]
            layer3_key = layer3_result.key_findings[:2] if layer3_result.key_findings else ["国内影响分析完成"]
            layer4_key = layer4_result.key_findings[:2] if layer4_result.key_findings else ["标的筛选完成"]

            # 计算整体置信度
            overall_confidence = self._calculate_overall_confidence([
                layer1_result, layer2_result, layer3_result, layer4_result
            ])

            # 生成投资逻辑链条
            logic_chain = self._extract_investment_logic_chain(
                layer1_result, layer2_result, layer3_result, layer4_result
            )

            summary = f"""
# 四层漏斗思维链深度分析报告

## 📊 分析框架概述
本报告采用"由外向内、由宏观到微观、由影响到标的"的四层漏斗模型，通过系统性的信息差挖掘，识别出完整的投资机会链条。

## 🎯 核心投资逻辑链条
{logic_chain}

## 🔍 第一层：事件感知与直接联想
**关键发现：**
- {layer1_key[0]}
- {layer1_key[1] if len(layer1_key) > 1 else "事件要素分析完成"}

## 💡 第二层：供应链信息差挖掘（核心价值层）
**关键信息差：**
- {layer2_key[0]}
- {layer2_key[1] if len(layer2_key) > 1 else "供应链深度分析完成"}

## 🏭 第三层：国内产业影响分析
**市场影响：**
- {layer3_key[0]}
- {layer3_key[1] if len(layer3_key) > 1 else "产业影响评估完成"}

## 🎯 第四层：投资标的精准筛选
**标的筛选结果：**
- {layer4_key[0]}
- {layer4_key[1] if len(layer4_key) > 1 else "标的评估完成"}

## 📈 整体评估
- **分析置信度**：{overall_confidence:.1%}
- **投资时机**：{self._assess_investment_timing(layer1_result, layer3_result)}
- **风险等级**：{self._assess_risk_level(layer2_result, layer4_result)}
- **预期收益区间**：{self._extract_return_range(layer4_result)}
- **建议持有期**：{self._extract_time_horizon(layer4_result)}

## ⚠️ 风险提示
- 分析基于当前可获得信息，市场情况可能发生变化
- 投资决策需结合个人风险承受能力
- 建议关注相关政策变化和行业动态
- 设置合理的止损位，控制投资风险

---
*本报告基于四层漏斗思维链模型生成，重点挖掘信息差价值，为投资决策提供系统性分析支持。*
"""
            return summary

        except Exception as e:
            logger.error(f"生成分析总结失败: {e}")
            return "四层思维链分析已完成，请查看详细结果。"

    def _extract_investment_logic_chain(
        self,
        layer1_result: LayerAnalysisResult,
        layer2_result: LayerAnalysisResult,
        layer3_result: LayerAnalysisResult,
        layer4_result: LayerAnalysisResult
    ) -> str:
        """提取投资逻辑链条"""
        try:
            return f"""
**事件触发** → **供应链冲击** → **国内机会** → **精准标的**

1. **宏观事件**：基于第一层分析，识别出具有投资价值的市场事件
2. **信息差挖掘**：通过第二层深度分析，发现被市场忽视的关键信息
3. **国内传导**：第三层分析将全球影响具体化为国内投资机会
4. **标的锁定**：第四层精准筛选出最优投资标的

**核心优势**：避开大众认知"红海"，聚焦信息差"蓝海"机会
"""
        except:
            return "投资逻辑链条分析中..."

    def _assess_investment_timing(self, layer1_result: LayerAnalysisResult, layer3_result: LayerAnalysisResult) -> str:
        """评估投资时机"""
        try:
            # 简化的时机评估逻辑
            if "紧急" in layer1_result.analysis_content or "立即" in layer3_result.analysis_content:
                return "立即关注"
            elif "短期" in layer3_result.analysis_content:
                return "短期机会"
            else:
                return "中长期布局"
        except:
            return "待评估"

    def _assess_risk_level(self, layer2_result: LayerAnalysisResult, layer4_result: LayerAnalysisResult) -> str:
        """评估风险等级"""
        try:
            # 基于置信度和分析内容评估风险
            avg_confidence = (layer2_result.confidence_score + layer4_result.confidence_score) / 2
            if avg_confidence >= 0.8:
                return "中低风险"
            elif avg_confidence >= 0.6:
                return "中等风险"
            else:
                return "中高风险"
        except:
            return "中等风险"

    def _extract_return_range(self, layer4_result: LayerAnalysisResult) -> str:
        """提取收益区间"""
        try:
            import re
            content = layer4_result.analysis_content
            # 查找收益相关的百分比
            returns = re.findall(r'([+-]?\d+(?:\.\d+)?%)', content)
            if returns:
                return f"{min(returns)} - {max(returns)}"
            return "待评估"
        except:
            return "10-30%"

    def _extract_time_horizon(self, layer4_result: LayerAnalysisResult) -> str:
        """提取时间框架"""
        try:
            content = layer4_result.analysis_content
            if "短期" in content or "1-3个月" in content:
                return "3-6个月"
            elif "中期" in content or "6个月" in content:
                return "6-12个月"
            elif "长期" in content or "1年" in content:
                return "1年以上"
            return "6-12个月"
        except:
            return "6-12个月"
